import { useCallback, useMemo, useState } from "react";
import styles from '../Layout.module.scss';
import { PreloadImage } from "@/components/Image";
import libraryAdd from "@/Resources/icon/libraryAdd.png";
import lib_manager_icon from '@/Resources/icon/library_manager_icon.png';
import lib_icon from '@/Resources/icon/library_icon.png';
import { useHistory } from "react-router-dom";
import CreateLibraryModal from "@/pages/FATWall/FATWall_PC/LibraryManagement/CreateLibraryModal";
import { useVideoLibraryList } from "@/hooks/useVideoLibrary";
import { Modal } from 'antd';

import { useTheme } from '@/utils/themeDetector';
import libraryAdd_light from '@/Resources/icon/libraryAdd_light.png';
const useFATSideBar = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const {myLibraries, libraries, getLib, setLibraries } = useVideoLibraryList();
  const { isDarkMode } = useTheme() as any;

  const history = useHistory();

  // 侧边栏item点击回调

  const callback = useCallback((item) => {
    // 存储媒体库状态到localStorage
    const libraryState = {
      title: item.name,
      lib_id: item.lib_id
    };
    localStorage.setItem('libraryState', JSON.stringify(libraryState));
    
    history.push({ pathname: `/filmAndTelevisionWall_pc/library/${item.lib_id}`, state: { data: item, title: item.name } }) // 传递页面标题，layout中获取显示
  }, [history])

  const toLibraryManagement = useCallback(() => {
    history.push({ pathname: '/filmAndTelevisionWall_pc/libraryManagement', state: { title: '媒体库管理' } })
  }, [history])

  // 处理创建媒体库
  const handleCreateLibrary = useCallback(() => {
    // 检查是否已达到创建上限（5个媒体库）
    if (myLibraries.length >= 5) {
      Modal.info({
        title: '',
        icon: null, // 移除左上角图标
        content: (
          <div>
            <p style={{ textAlign: 'center', fontSize: '16px', color: 'var(--text-color)', margin: '0 0 20px 0', fontFamily: 'MiSans', fontWeight: '500', padding: '0 25px' }}>
              你创建的媒体库已满5个，无法继续，请删除后重试
            </p>
          </div>
        ),
        okText: '知道了',
        okButtonProps: {
          style: {
            backgroundColor: 'var(--user-selector-checkbox-bg)',
            color: 'var(--button-text-color)',
            border: 'none',
            borderRadius: '32px',
            width: '320px',
            height: '50px',
            fontFamily: 'MiSans',
            fontWeight: '500',
            fontSize: '16px'
          }
        },
        centered: true,
        width: 368,
        height: 170,
        maskClosable: false,
        closable: false,
        className: styles.limitModal
      });
      return;
    }

    // 如果未达到上限，正常打开创建弹窗
    setIsCreateModalOpen(true);
  }, [myLibraries.length]);

  // 处理创建弹窗关闭
  const handleCreateModalClose = useCallback(() => {
    setIsCreateModalOpen(false);
  }, []);

  // 处理创建成功
  const handleCreateSuccess = useCallback(() => {
    getLib(); // 刷新侧边栏媒体库列表
    // 创建成功后跳转到最近添加页面
    history.push('/filmAndTelevisionWall_pc/recently/recentlyAdd');
  }, [getLib, history]);

  const baseComponent = useMemo(() => {
    return (
      <div className={styles.fat_side_bar_container}>
        <div className={styles.fat_side_bar_header}>
          <span>媒体库</span>
          <PreloadImage onClick={handleCreateLibrary} src={isDarkMode ? libraryAdd_light : libraryAdd} alt="add" />
        </div>
        <div className={styles.fat_side_bar_content}>
          <div className={styles.fat_side_bar_content_item} onClick={() => toLibraryManagement()}>
            <PreloadImage src={lib_manager_icon} alt="library_manager" />
            <span>媒体库管理</span>
          </div>
          {
            libraries.slice().reverse().map((it) => (
              <div key={it.lib_id} className={styles.fat_side_bar_content_item} onClick={() => callback(it)}>
                <PreloadImage src={lib_icon} alt="library_file" />
                <span>{it.name}</span>
              </div>
            ))
          }
        </div>
      </div>
    )
  }, [callback, isDarkMode,libraries, toLibraryManagement, handleCreateLibrary])

  return {
    fatComponents: useMemo(() => (
      <>
        {baseComponent}
        <CreateLibraryModal
          visible={isCreateModalOpen}
          onClose={handleCreateModalClose}
          onSuccess={handleCreateSuccess}
          libraryCount={libraries.length}
        />
      </>
    ), [baseComponent, handleCreateModalClose, handleCreateSuccess, isCreateModalOpen, libraries.length]),
    libraries,
    setLibraries,
    getLib
  }
}

export default useFATSideBar;